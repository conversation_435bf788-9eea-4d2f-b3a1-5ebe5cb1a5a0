/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.hy.checkin.utils;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * 二维码生成工具类
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
@Slf4j
public class QRCodeUtil {

	/**
	 * 默认二维码宽度
	 */
	private static final int DEFAULT_WIDTH = 300;

	/**
	 * 默认二维码高度
	 */
	private static final int DEFAULT_HEIGHT = 300;

	/**
	 * 默认图片格式
	 */
	private static final String DEFAULT_FORMAT = "PNG";

	/**
	 * 生成二维码并返回Base64编码的图片数据
	 *
	 * @param content 二维码内容
	 * @return Base64编码的图片数据
	 */
	public static String generateQRCodeBase64(String content) {
		return generateQRCodeBase64(content, DEFAULT_WIDTH, DEFAULT_HEIGHT);
	}

	/**
	 * 生成二维码并返回Base64编码的图片数据
	 *
	 * @param content 二维码内容
	 * @param width   二维码宽度
	 * @param height  二维码高度
	 * @return Base64编码的图片数据
	 */
	public static String generateQRCodeBase64(String content, int width, int height) {
		try {
			// 设置二维码参数
			Map<EncodeHintType, Object> hints = new HashMap<>();
			hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.M);
			hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
			hints.put(EncodeHintType.MARGIN, 1);

			// 生成二维码
			QRCodeWriter qrCodeWriter = new QRCodeWriter();
			BitMatrix bitMatrix = qrCodeWriter.encode(content, BarcodeFormat.QR_CODE, width, height, hints);

			// 创建BufferedImage
			BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
			for (int x = 0; x < width; x++) {
				for (int y = 0; y < height; y++) {
					image.setRGB(x, y, bitMatrix.get(x, y) ? Color.BLACK.getRGB() : Color.WHITE.getRGB());
				}
			}

			// 转换为Base64
			ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
			ImageIO.write(image, DEFAULT_FORMAT, outputStream);
			byte[] imageBytes = outputStream.toByteArray();
			String base64Image = Base64.getEncoder().encodeToString(imageBytes);

			return "data:image/png;base64," + base64Image;

		} catch (WriterException | IOException e) {
			log.error("生成二维码失败: {}", e.getMessage(), e);
			throw new RuntimeException("生成二维码失败", e);
		}
	}

	/**
	 * 生成用户签到码
	 * 格式：ATT_userId_agendaId_timestamp_randomNum
	 *
	 * @param userId   用户ID
	 * @param agendaId 议程ID
	 * @return 签到码
	 */
	public static String generateCheckinCode(Long userId, Integer agendaId) {
		long timestamp = System.currentTimeMillis();
		int randomNum = (int) (Math.random() * 9000) + 1000; // 4位随机数
		return String.format("ATT_%d_%d_%d_%d", userId, agendaId, timestamp, randomNum);
	}

	/**
	 * 生成议程签到码
	 * 格式：AGENDA_agendaId_timestamp_randomNum
	 *
	 * @param agendaId 议程ID
	 * @return 议程签到码
	 */
	public static String generateAgendaCheckinCode(Integer agendaId) {
		long timestamp = System.currentTimeMillis();
		int randomNum = (int) (Math.random() * 9000) + 1000; // 4位随机数
		return String.format("AGENDA_%d_%d_%d", agendaId, timestamp, randomNum);
	}

	/**
	 * 验证签到码格式
	 *
	 * @param checkinCode 签到码
	 * @return 是否有效
	 */
	public static boolean isValidCheckinCode(String checkinCode) {
		if (checkinCode == null || checkinCode.trim().isEmpty()) {
			return false;
		}
		// 验证格式：ATT_userId_agendaId_timestamp_randomNum
		return checkinCode.startsWith("ATT_") && checkinCode.split("_").length == 5;
	}

	/**
	 * 从签到码中提取用户ID
	 *
	 * @param checkinCode 签到码
	 * @return 用户ID
	 */
	public static Long extractUserIdFromCode(String checkinCode) {
		if (!isValidCheckinCode(checkinCode)) {
			return null;
		}
		try {
			String[] parts = checkinCode.split("_");
			return Long.parseLong(parts[1]);
		} catch (NumberFormatException e) {
			log.warn("无法从签到码中提取用户ID: {}", checkinCode);
			return null;
		}
	}

	/**
	 * 从签到码中提取议程ID
	 *
	 * @param checkinCode 签到码
	 * @return 议程ID
	 */
	public static Integer extractAgendaIdFromCode(String checkinCode) {
		if (!isValidCheckinCode(checkinCode)) {
			return null;
		}
		try {
			String[] parts = checkinCode.split("_");
			return Integer.parseInt(parts[2]);
		} catch (NumberFormatException e) {
			log.warn("无法从签到码中提取议程ID: {}", checkinCode);
			return null;
		}
	}

}
