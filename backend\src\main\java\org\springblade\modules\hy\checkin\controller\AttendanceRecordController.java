/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.hy.checkin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.modules.hy.checkin.pojo.dto.CheckinRequestDTO;
import org.springblade.modules.hy.checkin.pojo.dto.QRCodeResponseDTO;
import org.springblade.modules.hy.checkin.pojo.entity.AttendanceRecordEntity;
import org.springblade.modules.hy.checkin.pojo.vo.AttendanceRecordVO;
import org.springblade.modules.hy.checkin.service.IAttendanceRecordService;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 参会签到记录表 控制器
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("attendance")
@Tag(name = "签到管理", description = "会议签到相关功能，包括生成二维码、扫码签到、查询签到状态等")
public class AttendanceRecordController extends BladeController {

	private final IAttendanceRecordService attendanceRecordService;

	/**
	 * 生成议程签到二维码（不需要用户认证）
	 */
	@GetMapping("/agenda/{agendaId}/qrcode")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "生成议程签到二维码", description = "为指定议程生成统一的签到二维码")
	@ApiResponses({
		@ApiResponse(responseCode = "200", description = "生成成功"),
		@ApiResponse(responseCode = "500", description = "服务器内部错误")
	})
	public R<QRCodeResponseDTO> generateAgendaQRCode(
		@Parameter(description = "议程ID", required = true) @PathVariable Long agendaId) {

		if (agendaId == null) {
			return R.fail("议程ID不能为空");
		}

		try {
			QRCodeResponseDTO response = attendanceRecordService.generateAgendaQRCode(agendaId);
			return R.data(response, "二维码生成成功");
		} catch (Exception e) {
			log.error("生成二维码失败", e);
			return R.fail("生成二维码失败：" + e.getMessage());
		}
	}

	/**
	 * 议程签到
	 */
	@PostMapping("/checkin/{agendaId}")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "议程签到", description = "用户对指定议程进行签到")
	@ApiResponses({
		@ApiResponse(responseCode = "200", description = "签到成功"),
		@ApiResponse(responseCode = "400", description = "请求参数错误"),
		@ApiResponse(responseCode = "401", description = "未授权"),
		@ApiResponse(responseCode = "500", description = "服务器内部错误")
	})
	public R<Map<String, Object>> checkin(@Parameter(description = "议程ID", required = true) @PathVariable Long agendaId) {
		BladeUser user = AuthUtil.getUser();
		if (user == null) {
			return R.fail("用户未登录");
		}

		if (agendaId == null) {
			return R.fail("议程ID不能为空");
		}

		try {
			AttendanceRecordEntity record = attendanceRecordService.checkinByAgenda(agendaId, user.getUserId());

			if (record == null) {
				// 检查是否已经签到过了
				if (attendanceRecordService.isCheckedIn(user.getUserId(), agendaId)) {
					return R.fail("您已经签到过了，请勿重复签到");
				} else {
					return R.fail("签到失败，请重试");
				}
			}

			Map<String, Object> result = new HashMap<>();
			result.put("success", true);
			result.put("checkinTime", record.getCheckinTime());
			result.put("message", "签到成功");
			result.put("recordId", record.getId());
			result.put("agendaId", agendaId);
			result.put("userId", user.getUserId());

			return R.data(result, "签到成功");
		} catch (Exception e) {
			log.error("签到失败", e);
			return R.fail("签到失败：" + e.getMessage());
		}
	}

	/**
	 * 验证二维码并签到
	 */
	@PostMapping("/checkin/verify")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "验证二维码并签到", description = "验证二维码的完整性后进行签到")
	@ApiResponses({
		@ApiResponse(responseCode = "200", description = "签到成功"),
		@ApiResponse(responseCode = "400", description = "请求参数错误"),
		@ApiResponse(responseCode = "401", description = "未授权"),
		@ApiResponse(responseCode = "500", description = "服务器内部错误")
	})
	public R<Map<String, Object>> verifyAndCheckin(
		@Parameter(description = "议程ID", required = true) @RequestParam Long agendaId,
		@Parameter(description = "签到码", required = true) @RequestParam String code,
		@Parameter(description = "时间戳", required = true) @RequestParam Long timestamp,
		@Parameter(description = "签名", required = true) @RequestParam String signature) {

		BladeUser user = AuthUtil.getUser();
		if (user == null) {
			return R.fail("用户未登录");
		}

		try {
			// 验证URL签名
			if (!attendanceRecordService.verifyUrlSignature(agendaId.intValue(), code, timestamp, signature)) {
				return R.fail("二维码验证失败，请使用有效的签到二维码");
			}

			// 验证通过后进行签到
			AttendanceRecordEntity record = attendanceRecordService.checkinByAgenda(agendaId, user.getUserId());

			if (record == null) {
				// 检查是否已经签到过了
				if (attendanceRecordService.isCheckedIn(user.getUserId(), agendaId)) {
					return R.fail("您已经签到过了，请勿重复签到");
				} else {
					return R.fail("签到失败，请重试");
				}
			}

			Map<String, Object> result = new HashMap<>();
			result.put("success", true);
			result.put("checkinTime", record.getCheckinTime());
			result.put("message", "签到成功");
			result.put("recordId", record.getId());
			result.put("agendaId", agendaId);
			result.put("userId", user.getUserId());

			return R.data(result, "签到成功");
		} catch (Exception e) {
			log.error("验证签到失败", e);
			return R.fail("签到失败：" + e.getMessage());
		}
	}

	/**
	 * 查询签到状态
	 */
	@GetMapping("/status")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "查询签到状态", description = "查询当前用户的签到状态")
	@ApiResponses({
		@ApiResponse(responseCode = "200", description = "查询成功"),
		@ApiResponse(responseCode = "401", description = "未授权"),
		@ApiResponse(responseCode = "500", description = "服务器内部错误")
	})
	public R<Map<String, Object>> getCheckinStatus(
		@Parameter(description = "议程ID（可选）") @RequestParam(required = false) Long agendaId) {
		
		BladeUser user = AuthUtil.getUser();
		if (user == null) {
			return R.fail("用户未登录");
		}
		
		try {
			AttendanceRecordEntity record = attendanceRecordService.getCheckinStatus(user.getUserId(), agendaId);
			boolean isCheckedIn = record != null;
			
			Map<String, Object> result = new HashMap<>();
			result.put("isCheckedIn", isCheckedIn);
			result.put("userId", user.getUserId());
			
			if (isCheckedIn) {
				result.put("checkinTime", record.getCheckinTime());
				result.put("agendaId", record.getHyAgendaId());
			}
			
			return R.data(result, "查询成功");
		} catch (Exception e) {
			log.error("查询签到状态失败", e);
			return R.fail("查询失败：" + e.getMessage());
		}
	}

	/**
	 * 签到记录分页查询
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "签到记录分页查询", description = "分页查询签到记录")
	public R<IPage<AttendanceRecordVO>> list(AttendanceRecordVO attendanceRecord, Query query) {
		IPage<AttendanceRecordVO> pages = attendanceRecordService.selectAttendanceRecordPage(Condition.getPage(query), attendanceRecord);
		return R.data(pages);
	}

	/**
	 * 签到记录详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "签到记录详情", description = "查询签到记录详情")
	public R<AttendanceRecordEntity> detail(AttendanceRecordEntity attendanceRecord) {
		AttendanceRecordEntity detail = attendanceRecordService.getOne(Condition.getQueryWrapper(attendanceRecord));
		return R.data(detail);
	}

}
